// 测试 uTconvert 函数
function uTconvert(value, type) {
  if (!value) {
    return '';
  }
  
  if (type === '元') {
    // 对于金额，需要转换为11位的字符串（对应亿千百十万千百十元角分）
    const yuan = Number(value);
    const fen = Math.round(yuan * 100); // 转换为分并四舍五入
    let str = fen.toString();
    
    // 确保字符串长度为11位，不足的在前面补0
    str = str.padStart(11, '0');
    
    return [...str].reverse().join('');
  } else {
    const str = value.toString();
    return [...str].reverse().join('');
  }
}

// 测试
console.log('测试金额 33982.3:');
const result = uTconvert(33982.3, '元');
console.log('转换结果:', result);
console.log('字符串长度:', result.length);

// 分析每一位
const units = ['亿', '千', '百', '十', '万', '千', '百', '十', '元', '角', '分'];
console.log('\n各位数字:');
for (let i = 0; i < units.length; i++) {
  const digit = result[units.length - 1 - i] || '';
  console.log(`${units[i]}: ${digit}`);
}

// 找到最高位的非零数字位置（按单位从高到低）
let highestNonZeroUnit = -1;
const unitslength = units.length;
for (let i = 0; i < units.length; i++) {
  const valueIndex = unitslength - 1 - i;
  const digit = result[valueIndex] || '';
  if (digit !== '0' && digit !== '') {
    highestNonZeroUnit = i;
    console.log(`最高位非零数字在 ${units[i]} 位，值为 ${digit}`);
    break;
  }
}

console.log('\n应该显示的位数:');
for (let i = 0; i < units.length; i++) {
  const valueIndex = unitslength - 1 - i;
  const digit = result[valueIndex] || '';
  const show = i >= highestNonZeroUnit;
  console.log(`${units[i]}: ${digit} (显示: ${show})`);
}
