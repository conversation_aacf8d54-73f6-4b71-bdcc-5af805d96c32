<script setup lang="ts">
import { computed } from 'vue';

interface Props {
    /**
     * 给ul添加的class name
     */
    classname?: string;
    value?: string;
    type?: string;
}
const props = withDefaults(defineProps<Props>(), {
    classname: '',
    value: '',
    type: '',
});

const units: string[] = ['亿', '千', '百', '十', '万', '千', '百', '十', '元', '角', '分'];
const unitslength: number = units.length;

// 计算应该显示的数字和单位
const displayItems = computed(() => {
    if (props.type === 'head') {
        return units.map(unit => ({ unit, digit: unit, show: true }));
    }

    if (!props.value) {
        return units.map(unit => ({ unit, digit: '', show: false }));
    }

    // 找到最高位的非零数字位置（从左到右，即从高位到低位）
    let highestNonZeroUnit = -1;
    for (let i = 0; i < units.length; i++) {
        const valueIndex = unitslength - 1 - i;
        const digit = props.value[valueIndex] || '';
        if (digit !== '0' && digit !== '') {
            highestNonZeroUnit = i;
            break;
        }
    }

    // 如果全是0，至少显示元位的0
    if (highestNonZeroUnit === -1) {
        return units.map((unit, i) => ({
            unit,
            digit: i === 8 ? '0' : '', // 元位显示0
            show: i === 8
        }));
    }

    return units.map((unit, i) => {
        const valueIndex = unitslength - 1 - i;
        const digit = props.value[valueIndex] || '';
        // 从最高位非零数字开始显示到分位
        const show = i >= highestNonZeroUnit;
        return { unit, digit, show };
    });
});
</script>
<template>
    <ul :class="[classname]">
        <li
            v-for="(item, i) in displayItems"
            :key="item.unit"
            v-show="item.show"
        >
            {{ props.type === 'head' ? item.unit : item.digit }}
        </li>
    </ul>
</template>
<style lang="scss" scoped>
ul {
    pointer-events: none;
}
</style>
